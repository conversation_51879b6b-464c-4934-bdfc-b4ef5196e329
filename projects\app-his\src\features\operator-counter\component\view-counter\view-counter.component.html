<div class="bg-gray-50 h-full p-2" ecui-height-fill>
    <!-- Compact Header -->
    <div class="bg-white rounded-lg shadow-sm border p-2 mb-2">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <mat-icon class="text-white text-lg" svgIcon="mat_outline:price_change"></mat-icon>
                </div>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">Counter Operator</h1>
                    <p class="text-base text-gray-600">{{todayDate | date: "dd-MM-yyyy HH:mm"}}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <div class="flex items-center space-x-2 bg-gray-50 rounded px-2 py-1">
                    <span class="text-base text-gray-600">Logins:</span>
                    <span class="text-lg font-bold text-blue-600">{{loginCount}}</span>
                </div>
                <div class="flex items-center space-x-2 bg-gray-50 rounded px-2 py-1">
                    <span class="text-base text-gray-600">Sessions:</span>
                    <span class="text-lg font-bold text-green-600">{{sessionCount}}</span>
                </div>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-base" (click)="print()">
                    <mat-icon class="text-white text-base" svgIcon="mat_solid:print"></mat-icon>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-12 gap-2 ]">
        <!-- Left Panel: Status & Info -->
        <div class="col-span-4 space-y-2">
            <!-- Counter Status -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-base font-semibold text-gray-700">COUNTER STATUS</h3>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 rounded-full" [ngClass]="counterStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                        <span class="text-base font-bold" [ngClass]="counterStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{counterStatus}}</span>
                    </div>
                </div>
                <div class="text-base text-gray-600">Terminal: {{counterDes}}</div>
            </div>

            <!-- Session Status -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-base font-semibold text-gray-700">SESSION STATUS</h3>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 rounded-full" [ngClass]="sessionStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                        <span class="text-base font-bold" [ngClass]="sessionStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{sessionStatus}}</span>
                    </div>
                </div>
                <div class="text-base text-gray-600">Active Sessions: {{sessionCount}}</div>
            </div>

            <!-- Financial Info -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-2">FINANCIAL</h3>
                <div class="grid grid-cols-2 gap-2 text-base">
                    <div>
                        <span class="text-gray-600">Float:</span>
                        <div class="font-bold text-green-700">${{floatAmount || '0.00'}}</div>
                    </div>
                    <div>
                        <span class="text-gray-600">Opening:</span>
                        <div class="font-bold text-green-700">${{openingAmount || '0.00'}}</div>
                    </div>
                </div>
            </div>

            <!-- Time Info -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-2">SESSION TIME</h3>
                <div class="grid grid-cols-2 gap-2 text-base">
                    <div>
                        <span class="text-gray-600">Start:</span>
                        <div class="font-bold">{{startTime || 'Not Started'}}</div>
                    </div>
                    <div>
                        <span class="text-gray-600">End:</span>
                        <div class="font-bold">{{endTime || 'Active'}}</div>
                    </div>
                </div>
            </div>

            <!-- Current Operator -->
            <div class="bg-blue-50 rounded-lg border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-1">CURRENT OPERATOR</h3>
                <div class="flex items-center justify-between">
                    <span class="text-lg font-bold text-blue-700">{{counterDes}}</span>
                    <span class="text-base px-2 py-1 rounded" [ngClass]="startTime ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'">
                        {{startTime ? 'Active' : 'Inactive'}}
                    </span>
                </div>
            </div>
        </div>

        <!-- Center Panel: Control Buttons -->
        <div class="col-span-4 space-y-2">
            <div class="bg-white rounded-lg shadow-sm border p-3">
                <h3 class="text-lg font-semibold text-gray-900 mb-3 text-center">CONTROL CENTER</h3>

                <div class="space-y-2">
                    <!-- Counter Control Button -->
                    <button class="w-full flex items-center justify-center p-3 rounded-lg font-medium transition-colors"
                        [ngClass]="{
                            'bg-blue-600 hover:bg-blue-700 text-white': !disableCounter,
                            'bg-gray-200 text-gray-500 cursor-not-allowed': disableCounter
                        }" [disabled]="disableCounter" (click)="openCounter()">
                        <mat-icon class="text-lg mr-2" svgIcon="mat_outline:lock_open"></mat-icon>
                        <span class="text-lg font-semibold">{{counterButton}}</span>
                    </button>

                    <!-- Session Control Button -->
                    <button class="w-full flex items-center justify-center p-3 rounded-lg font-medium transition-colors"
                        [ngClass]="{
                            'bg-green-600 hover:bg-green-700 text-white': !disableSession,
                            'bg-gray-200 text-gray-500 cursor-not-allowed': disableSession
                        }" [disabled]="disableSession" (click)="session()">
                        <mat-icon class="text-lg mr-2" svgIcon="mat_outline:play_arrow"></mat-icon>
                        <span class="text-lg font-semibold">{{sessionButton}}</span>
                    </button>

                    <!-- Confirm Actuals Button -->
                    <button class="w-full flex items-center justify-center p-3 rounded-lg font-medium transition-colors"
                        [ngClass]="{
                            'bg-purple-600 hover:bg-purple-700 text-white': !disableConfirmActual,
                            'bg-gray-200 text-gray-500 cursor-not-allowed': disableConfirmActual
                        }" [disabled]="disableConfirmActual" (click)="confirmActuals()">
                        <mat-icon class="text-lg mr-2" svgIcon="mat_outline:check_circle"></mat-icon>
                        <span class="text-lg font-semibold">Confirm Actuals</span>
                    </button>
                </div>
            </div>

            <!-- System Status -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-2">SYSTEM STATUS</h3>
                <div class="flex justify-between items-center text-base">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Counter: {{counterStatus}}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Session: {{sessionStatus}}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>System: Online</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel: Statistics & Metrics -->
        <div class="col-span-4 space-y-2">
            <!-- Login Statistics -->
            <div class="bg-orange-50 rounded-lg border p-2">
                <div class="flex items-center justify-between mb-1">
                    <h3 class="text-base font-semibold text-gray-700">LOGIN SESSIONS</h3>
                    <mat-icon class="text-orange-600 text-lg" svgIcon="mat_outline:login"></mat-icon>
                </div>
                <div class="text-xl font-bold text-orange-700">{{loginCount}}</div>
                <div class="text-base text-gray-500">Active sessions</div>
            </div>

            <!-- Session Count -->
            <div class="bg-blue-50 rounded-lg border p-2">
                <div class="flex items-center justify-between mb-1">
                    <h3 class="text-base font-semibold text-gray-700">SESSION COUNT</h3>
                    <mat-icon class="text-blue-600 text-lg" svgIcon="mat_outline:schedule"></mat-icon>
                </div>
                <div class="text-xl font-bold text-blue-700">{{sessionCount}}</div>
                <div class="text-base text-gray-500">Total sessions today</div>
            </div>

            <!-- Date & Time Display -->
            <div class="bg-green-50 rounded-lg border p-2">
                <div class="flex items-center justify-between mb-1">
                    <h3 class="text-base font-semibold text-gray-700">CURRENT DATE</h3>
                    <mat-icon class="text-green-600 text-lg" svgIcon="mat_outline:calendar_today"></mat-icon>
                </div>
                <div class="text-lg font-bold text-green-700">{{todayDate | date: "dd-MM-yyyy"}}</div>
                <div class="text-base text-gray-500">{{todayDate | date: "EEEE"}}</div>
            </div>

            <!-- Real-time Clock -->
            <div class="bg-gray-50 rounded-lg border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-1">CURRENT TIME</h3>
                <div class="text-lg font-bold text-gray-800">{{todayDate | date: "HH:mm:ss"}}</div>
                <div class="text-base text-gray-500">Live time</div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <h3 class="text-base font-semibold text-gray-700 mb-2">QUICK ACTIONS</h3>
                <div class="flex space-x-1">
                    <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded text-base" (click)="print()">
                        <mat-icon class="text-base" svgIcon="mat_solid:print"></mat-icon>
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>