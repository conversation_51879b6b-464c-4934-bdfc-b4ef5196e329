import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { VisitViewComponent } from '../visit-view/visit-view.component';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { FillHeightDirective } from 'ec-ngcore/ui';
import {  VisitService } from 'ecmed-api/visitmgmt';
export interface IvisitsDialogOptions {
    height?: string;
    width?: string;
    title?: string;
    identifier?: any;
};
@Component({
    selector: 'his-visit-viewpopup',
    standalone: true,
    imports: [CommonModule, FormsModule, FillHeightDirective, MatButtonModule, MatIconModule, MatDialogModule,
        VisitViewComponent],
    templateUrl: './view-popup.component.html',
    styleUrl: './view-popup.component.scss'
})
export class VisitViewPopupComponent implements OnInit {
    private readonly DEFAULT_DIALOG_CONFIG = {
        width: '90vw',
        height: 'fit-content',
        disableClose: true,
        hasBackdrop: true
    };
    visit: any;
    appList: any;
    id: any;
    isPrintClicked: boolean = false;
    public static openDialog(dialog: MatDialog, options: IvisitsDialogOptions, identifier?: any): MatDialogRef<any> {
        const dialogOptions = { ...options, identifier }; // Ensure identifier is part of the options
        return dialog.open(VisitViewPopupComponent, {
            data: dialogOptions,
            width: "90vw",
            height: "fit-content",
            disableClose: true,
            hasBackdrop: true
        });
    }
  

    public VISIT_IDENTIFIER?: string;
    constructor(@Inject(MAT_DIALOG_DATA) public data: IvisitsDialogOptions, 
        private route: ActivatedRoute, public router: Router, public _API:VisitService,
        private dialogRef: MatDialogRef<VisitViewPopupComponent>
    ) {
        if (!this.data) {
            this.data = { title: 'View Patient Details' }; // Initialize with default title if data is undefined
        }
        if (this.data.identifier) {
            this.VISIT_IDENTIFIER = this.data.identifier;
            // this.patientView(this.PAT_IDENTIFIER);
        }
    }
    ngOnInit() {
        this.route.params.subscribe(params => {
            const id = params['id'];
            this.id = id;
            this.getvisitviewid();
        });
    }

    onPrint() {
        this.isPrintClicked = true;
        window.print();
    }

    public getvisitviewid() {
        if (this.VISIT_IDENTIFIER)
            this._API.visitGetVisitAppointmentDetailsGet({id:this.VISIT_IDENTIFIER}).subscribe(
                (result) => {
                    this.visit = result;
                }
            );
    }

    goback() {
        this.router.navigate(['visits/view'])
    }

}
