import { Component, EventEmitter, Output } from '@angular/core';
import { MaterialPackage } from '@app-his/utils';
import { CommonModule } from '@angular/common';
import { MatDialog,MatDialogRef } from '@angular/material/dialog';
@Component({
  selector: 'ec-close-session-pop-up',
  standalone: true,
  imports: [MaterialPackage,CommonModule],
  templateUrl: './close-session-pop-up.component.html',
  styleUrl: './close-session-pop-up.component.scss'
})
export class CloseSessionPopUpComponent {

  @Output() actuals = new EventEmitter();

  constructor(public _dialogRef:MatDialogRef<CloseSessionPopUpComponent>,public _dialog:MatDialog){}

  data="abc";
  addActual="No";

  public addActuals(){
    this._dialogRef.close({data:0});
  }
  
  public showActuals(){
    this._dialogRef.close({data:1});
    
  }

  public closePopUp(){
    this._dialogRef.close({data:0});
  }
}
