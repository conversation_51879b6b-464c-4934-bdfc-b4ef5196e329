/* Simple Counter Operator Styles */

/* <PERSON>ton styles */
button {
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Card styles */
.bg-white {
  transition: box-shadow 0.2s ease;
}

.bg-white:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr !important;
  }

  .md\\:grid-cols-3 {
    grid-template-columns: 1fr !important;
  }

  .md\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .md\\:flex-row {
    flex-direction: column !important;
  }
}

@media (max-width: 480px) {
  .p-6 {
    padding: 1rem !important;
  }

  .p-4 {
    padding: 0.75rem !important;
  }

  .gap-4 {
    gap: 0.5rem !important;
  }
}

/* Print styles */
@media print {
  button {
    display: none !important;
  }

  .bg-white {
    background: white !important;
    box-shadow: none !important;
  }
}