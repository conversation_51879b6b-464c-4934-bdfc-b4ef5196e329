import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { MaterialPackage } from '@his-components/utils';
import { FormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { ApptViewPopupComponent } from '../../appointments';
import { MatDialog } from '@angular/material/dialog';
import { PatientViewPopupComponent } from '../../patients';
import { CommonService } from '../../patients';
import { EcSysTextService } from 'ec-ngcore';

@Component({
    selector: 'his-visit-view',
    standalone: true,
    imports: [CommonModule, MaterialPackage,  MatSnackBarModule, FormsModule, FillHeightDirective],
    templateUrl: './visit-view.component.html',
    styleUrl: './visit-view.component.scss'
})
export class VisitViewComponent {
    constructor(public _sysText: EcSysTextService,
        public _dialog: MatDialog,
        public commServ: CommonService,
        public router: Router) { }


    _visit?: any;
    _payerDetails: any = [];
    @Input() showPayerDetails: boolean = true
    @Output() add = new EventEmitter<void>();
    @Output() cancel = new EventEmitter<void>();
    @Input() dialogState: any

    @Input() set visit(value: any) {
        console.log(value);
        if (value ) {
            this._visit = Array.isArray(value) ? value[0] :value;
        }
    }

    @Input() set payerDetails(value: any) {
        if (value?.length) {
            this._payerDetails = value;
        }
    }


    openDialog(PAT_IDENTIFIER: any) {
        PatientViewPopupComponent.openDialog(this._dialog, { "title": "View Patient Details" }, PAT_IDENTIFIER);
    }

    goBack(): void {
        this.cancel.emit();
    }

    apptDetails(ApptId:string) {
        ApptViewPopupComponent.openDialog(this._dialog, { "title": "View Appointment Details" }, ApptId)
    }
    public handleFormatDate() {
        // return this.datePipe.transform(date, 'dd/MM/yyyy hh:mm a') || '';
        //for date
        let today = new Date()
        let day = String(today.getDate()).padStart(2, '0');
        let month = String(today.getMonth() + 1).padStart(2, '0');
        let year = today.getFullYear();
        //for time
        let hours = today.getHours();
        let minutes = String(today.getMinutes()).padStart(2, '0');
        let ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12;
        let formattedHours = String(hours).padStart(2, '0');

        return `${day}/${month}/${year} ${formattedHours}:${minutes} ${ampm}`;
    }

    calculateAge(dob:any): number {
        return this.commServ.calculateAge(dob);
    }

    public handlePrint() {
        window.print();
    }

    public handleBack() {
        if (window?.location?.href?.includes("service/visit/view/")) {
            this.router.navigate(['his-psd/service'])
        } else if (window?.location?.href?.includes("appointmentqueue/view/")) {
            this.router.navigate(['his-psd/appointmentqueue'])
        } else {
            this.router.navigate(['visits/view'])
        }
    }

    public handlePayerShow() {

    }

}
