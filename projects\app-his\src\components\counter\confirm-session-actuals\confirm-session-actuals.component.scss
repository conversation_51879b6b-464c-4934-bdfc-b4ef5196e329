/* Confirm Session Actuals - Clean Modern Design */

/* Table styling */
.mat-mdc-table {
    background: transparent;

    .mat-mdc-header-row {
        height: 32px;
    }

    .mat-mdc-row {
        height: 32px;
        transition: background-color 0.2s ease;
    }

    .mat-mdc-row:hover {
        background-color: #f9fafb;
    }

    .mat-mdc-header-cell {
        font-size: 0.75rem;
        font-weight: 600;
        color: #374151;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        padding: 8px;
    }

    .mat-mdc-cell {
        font-size: 0.75rem;
        color: #1f2937;
        border-bottom: 1px solid #f3f4f6;
        padding: 8px;
    }
}

/* Input styling for editable amounts */
input[type="number"] {
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.75rem;
    text-align: center;

    &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 1px #3b82f6;
    }
}

/* Button hover effects */
button {
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Card hover effects */
.bg-white {
    transition: box-shadow 0.2s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .grid-cols-3 {
        grid-template-columns: 1fr !important;
    }

    .text-lg {
        font-size: 1rem !important;
    }

    .p-3 {
        padding: 0.5rem !important;
    }
}
