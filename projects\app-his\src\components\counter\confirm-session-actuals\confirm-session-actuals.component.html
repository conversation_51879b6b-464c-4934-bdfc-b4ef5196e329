<div class="bg-gray-50 h-full flex flex-col overflow-auto" >
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border p-3 m-2 mb-0">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <mat-icon class="text-white text-sm" svgIcon="mat_outline:check_circle"></mat-icon>
                </div>
                <h1 class="text-lg font-bold text-gray-900">
                    {{counterActual ? "Confirm Session Actuals" : "Counter Collection Actuals"}}
                </h1>
            </div>
            <button *ngIf="counterActual"
                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                (click)="back()">
                Back
            </button>
        </div>
    </div>

    <!-- Session Information Cards -->
    <div class="p-2 flex-shrink-0">
        <div class="grid grid-cols-3 gap-2">
            <!-- Left Card -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Login:</span>
                        <span class="text-sm font-bold">{{dataSource?.LOGIN}}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Start Time:</span>
                        <span class="text-sm font-bold">{{dataSource?.STIME}}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Float Amount:</span>
                        <span class="text-sm font-bold text-green-700">${{dataSource?.FLOATAMOUNT}}</span>
                    </div>
                </div>
            </div>

            <!-- Center Card -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Session:</span>
                        <span class="text-sm font-bold">{{dataSource?.SESSION}}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">End Time:</span>
                        <span class="text-sm font-bold">{{dataSource?.ETIME}}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Opening Cash:</span>
                        <span class="text-sm font-bold text-green-700">${{dataSource?.OPENINGAMOUNT}}</span>
                    </div>
                </div>
            </div>

            <!-- Right Card -->
            <div class="bg-white rounded-lg shadow-sm border p-2">
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">Status:</span>
                        <span class="text-sm font-bold">{{dataSource?.STATUS}}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-blue-600">User:</span>
                        <span class="text-sm font-bold">{{dataSource?.USER}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Counter Collection Actuals Table -->
    <div *ngIf="!counterActual" class="flex-1 flex flex-col p-2 overflow-hidden">
        <div class="bg-white rounded-lg shadow-sm border flex-1 flex flex-col overflow-hidden">
            <div class="p-2 border-b">
                <h3 class="text-sm font-semibold text-gray-900">Payment Details</h3>
            </div>

            <div class="flex-1 overflow-y-auto">
                <table mat-table [dataSource]="SessionDetails" class="w-full">
                    <ng-container matColumnDef="Payment Code">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2">
                            Payment Code
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2">
                            {{ element.PAYMENTCODE }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="Payment Des">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2">
                            Payment Description
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2">
                            {{ element.PAYMENTDES }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="Amount">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2 text-center">
                            Amount
                        </th>
                        <td mat-cell *matCellDef="let element; let i = index"
                            class="text-xs p-2 text-center bg-blue-50 cursor-pointer hover:bg-blue-100"
                            (click)="editAmount(i)">
                            <div *ngIf="element.EDIT" class="flex justify-center">
                                <input type="number" min="0"
                                    class="w-20 px-2 py-1 text-xs border rounded"
                                    [(ngModel)]="element.AMOUNT"
                                    (keyup.enter)="editDone(i)" />
                            </div>
                            <div *ngIf="!element.EDIT" class="font-medium">
                                ${{ element.AMOUNT.toFixed(2) }}
                            </div>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="Column" class="h-8"></tr>
                    <tr mat-row *matRowDef="let row; columns: Column" class="h-8 hover:bg-gray-50"></tr>
                </table>
            </div>

            <div class="p-3 border-t bg-gray-50 flex justify-center">
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium"
                    (click)="counterActuals()">
                    Confirm Actuals
                </button>
            </div>
        </div>
    </div>
    <!-- Session Actuals Confirmation Table -->
    <div *ngIf="counterActual" class="flex-1 flex flex-col p-2 overflow-hidden">
        <div class="bg-white rounded-lg shadow-sm border flex-1 flex flex-col overflow-hidden">
            <div class="p-2 border-b">
                <h3 class="text-sm font-semibold text-gray-900">Actuals Comparison</h3>
            </div>

            <div class="flex-1 overflow-y-auto">
                <table mat-table [dataSource]="SessionDetails" class="w-full">
                    <ng-container matColumnDef="Payment Code">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2">
                            Payment Code
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2">
                            {{ element.PAYMENTCODE }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="Actual Amount">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2 text-center">
                            Actual Amount
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2 text-center font-medium">
                            ${{ element.AMOUNT.toFixed(2) }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="System Amount">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2 text-center">
                            System Amount
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2 text-center font-medium">
                            ${{ element.AMOUNT.toFixed(2) }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="Difference">
                        <th mat-header-cell *matHeaderCellDef class="text-xs font-bold bg-gray-50 p-2 text-center">
                            Difference
                        </th>
                        <td mat-cell *matCellDef="let element" class="text-xs p-2 text-center font-medium">
                            <span [ngClass]="Difference >= 0 ? 'text-green-600' : 'text-red-600'">
                                ${{ Difference.toFixed(2) }}
                            </span>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="counterColumn" class="h-8"></tr>
                    <tr mat-row *matRowDef="let row; columns: counterColumn" class="h-8 hover:bg-gray-50"></tr>
                </table>
            </div>

            <div class="p-3 border-t bg-gray-50 flex justify-center">
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium"
                    (click)="closePopup()">
                    Finalize
                </button>
            </div>
        </div>
    </div>
</div>
